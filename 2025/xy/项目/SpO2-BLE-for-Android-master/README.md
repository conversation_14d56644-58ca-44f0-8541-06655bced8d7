# SpO2-BLE-for-Android

## 项目简介

SpO2-BLE-for-Android 是一个用于连接和监控SpO2血氧仪设备的Android应用程序。该应用通过蓝牙低功耗(BLE)技术与血氧仪设备进行通信，实时显示血氧饱和度、心率、灌注指数、呼吸率等生理参数，并提供数据记录和分享功能。

## 主要功能

### 🔗 蓝牙连接
- 自动扫描附近的SpO2血氧仪设备
- 支持多种设备协议（BCI、BCI-RESP、BERRY）
- 设备重命名功能
- 连接状态实时监控

### 📊 数据监控
- **血氧饱和度(SpO2)**: 实时显示血氧饱和度百分比
- **心率(PR)**: 实时显示心率数值
- **灌注指数(PI)**: 显示血液灌注强度
- **呼吸率(RR)**: 显示呼吸频率（支持协议）
- **波形显示**: 实时脉搏波形图

### 💾 数据管理
- CSV格式数据记录
- 时间戳精确到毫秒
- 数据文件分享功能
- 支持Android分区存储

### ⚙️ 设备配置
- 协议切换（BCI/BCI-RESP/BERRY）
- 频率调节（BERRY协议）
- 硬件/软件版本查询

## 技术特性

- **最低Android版本**: Android 8.0 (API 26)
- **目标Android版本**: Android 14 (API 34)
- **蓝牙技术**: 蓝牙低功耗(BLE)
- **权限管理**: 动态权限申请
- **文件存储**: 兼容Android分区存储
- **UI框架**: Material Design

## 项目结构

```
app/src/main/java/com/berry_med/bci/
├── MainActivity.java              # 主活动类
├── application/
│   └── MyApplication.java         # 应用程序类
├── blutooth/                      # 蓝牙功能模块
│   ├── Model.java                 # 设备模型配置
│   ├── MyBluetooth.java          # 蓝牙管理器
│   ├── ParseRunnable.java        # 数据解析线程
│   └── WaveForm.java             # 波形显示组件
├── dialog/                        # 对话框模块
│   ├── DeviceAdapter.java        # 设备列表适配器
│   └── MyDialog.java             # 设备选择对话框
└── utils/                         # 工具类模块
    ├── MyFiles.java              # 文件操作工具
    ├── Permissions.java          # 权限管理工具
    └── ToastUtil.java            # 提示工具
```

## 依赖库

- **AndroidX**: Android兼容库
- **Material Design**: UI组件库
- **XXPermissions**: 权限管理库
- **FastBLE**: 蓝牙低功耗通信库

## 常见问题

### 权限相关错误
**错误信息:**
```
if (never) XXPermissions.startPermissionActivity(activity, permissions);
No android.support.v4.app.Fragment found
```

**解决方案:**
在 `gradle.properties` 文件中添加：
```properties
android.enableJetifier=true
```

### 蓝牙连接问题
1. 确保设备支持蓝牙低功耗(BLE)
2. 检查位置权限是否已授权
3. 确认蓝牙已开启
4. 设备距离不要超过10米

### 数据记录问题
1. 确保存储权限已授权
2. 检查设备存储空间是否充足
3. 确认设备已正确连接

## 开发环境

- **Android Studio**: 2023.1.1 或更高版本
- **Gradle**: 8.0 或更高版本
- **Java**: JDK 8 或更高版本

## 许可证

本项目采用开源许可证，具体许可证信息请查看项目根目录下的LICENSE文件。

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交GitHub Issue
- 发送邮件至项目维护者
 