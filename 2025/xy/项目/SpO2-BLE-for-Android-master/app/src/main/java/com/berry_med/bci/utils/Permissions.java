package com.berry_med.bci.utils;

import android.app.Activity;

import androidx.annotation.NonNull;

import com.berry_med.bci.blutooth.MyBluetooth;
import com.berry_med.bci.dialog.MyDialog;
import com.hjq.permissions.OnPermissionCallback;
import com.hjq.permissions.Permission;
import com.hjq.permissions.XXPermissions;

import java.util.List;

/**
 * 权限管理工具类
 *
 * 主要功能：
 * 1. 管理应用所需的各种权限申请
 * 2. 处理权限授权和拒绝的回调
 * 3. 引导用户到设置页面开启权限
 *
 * 权限类型：
 * - 蓝牙相关权限（扫描、连接）
 * - 位置权限（蓝牙扫描需要）
 * - 存储权限（数据文件保存）
 *
 * @description Permissions
 * <AUTHOR>
 * @date 2024/10/23 10:45
 */
public class Permissions {

    /**
     * 申请所有必要权限
     * 包括存储、位置和蓝牙权限
     *
     * @param activity 当前Activity
     * @param ble 蓝牙管理器
     * @param dialog 设备选择对话框
     */
    public static void all(Activity activity, MyBluetooth ble, MyDialog dialog) {
        XXPermissions.with(activity)
                .permission(Permission.WRITE_EXTERNAL_STORAGE)    // 外部存储写入权限
                .permission(Permission.ACCESS_FINE_LOCATION)      // 精确位置权限
                .permission(Permission.ACCESS_COARSE_LOCATION)    // 粗略位置权限
                .permission(Permission.Group.BLUETOOTH)           // 蓝牙权限组
                .request(new OnPermissionCallback() {
                    @Override
                    public void onGranted(@NonNull List<String> permissions, boolean all) {
                        // 权限授权成功，检查蓝牙状态并显示设备选择对话框
                        if (ble != null) ble.isOpen(dialog);
                    }

                    @Override
                    public void onDenied(@NonNull List<String> permissions, boolean never) {
                        // 权限被拒绝，如果是永久拒绝则引导用户到设置页面
                        if (never) XXPermissions.startPermissionActivity(activity, permissions);
                    }
                });
    }

    /**
     * 申请存储权限
     * 用于数据文件的保存和读取
     *
     * @param activity 当前Activity
     */
    public static void storage(Activity activity) {
        XXPermissions.with(activity)
                .permission(Permission.WRITE_EXTERNAL_STORAGE)    // 外部存储写入权限
                .request(new OnPermissionCallback() {
                    @Override
                    public void onGranted(@NonNull List<String> permissions, boolean all) {
                        // 存储权限授权成功（无需特殊处理）
                    }

                    @Override
                    public void onDenied(@NonNull List<String> permissions, boolean never) {
                        // 存储权限被拒绝，如果是永久拒绝则引导用户到设置页面
                        if (never) XXPermissions.startPermissionActivity(activity, permissions);
                    }
                });
    }
}
