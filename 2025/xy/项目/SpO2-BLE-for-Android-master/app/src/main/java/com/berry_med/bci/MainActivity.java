package com.berry_med.bci;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.text.TextUtils;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.RadioGroup;
import android.widget.TextView;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.content.FileProvider;

import com.berry_med.bci.blutooth.Model;
import com.berry_med.bci.blutooth.MyBluetooth;
import com.berry_med.bci.blutooth.ParseRunnable;
import com.berry_med.bci.blutooth.WaveForm;
import com.berry_med.bci.dialog.DeviceAdapter;
import com.berry_med.bci.dialog.MyDialog;
import com.berry_med.bci.utils.MyFiles;
import com.berry_med.bci.utils.Permissions;
import com.berry_med.bci.utils.ToastUtil;

import java.io.File;
import java.util.List;

/**
 * SpO2血氧仪蓝牙连接主活动类
 *
 * 主要功能：
 * 1. 蓝牙设备搜索和连接
 * 2. 实时显示血氧饱和度(SpO2)、心率(PR)、灌注指数(PI)、呼吸率(RR)等生理参数
 * 3. 实时波形显示
 * 4. 数据记录和文件分享
 * 5. 设备协议切换（BCI、BCI-RESP、BERRY）
 * 6. 设备重命名功能
 *
 * <AUTHOR> Medical
 * @version 1.0
 */
public class MainActivity extends AppCompatActivity implements View.OnClickListener {

    // ==================== UI组件 ====================
    /** 协议选择单选按钮组 */
    private RadioGroup protocolRG;
    /** 频率设置布局容器 */
    private LinearLayout frequencyLayout;
    /** 频率选择单选按钮组 */
    private RadioGroup frequencyView;
    /** 数据包频率显示文本 */
    private TextView packetFreq;
    /** 设备名称显示文本 */
    private TextView name;
    /** 设备MAC地址显示文本 */
    private TextView mac;
    /** 硬件版本显示文本 */
    private TextView hw;
    /** 软件版本显示文本 */
    private TextView sw;
    /** 血氧饱和度显示文本 */
    private TextView spo2Tv;
    /** 心率显示文本 */
    private TextView prTv;
    /** 灌注指数显示文本 */
    private TextView piTv;
    /** 呼吸率标题文本 */
    private TextView rrTitle;
    /** 呼吸率显示文本 */
    private TextView rrTv;
    /** 波形显示自定义视图 */
    private WaveForm mWaveForm;
    /** 设备名称输入框 */
    private EditText inputDeviceName;

    // ==================== 核心功能组件 ====================
    /** 蓝牙管理器 */
    private MyBluetooth ble;
    /** 设备选择对话框 */
    private MyDialog dialog;
    /** 数据解析线程 */
    private ParseRunnable mParseRunnable;
    /** 文件操作工具 */
    private MyFiles myFiles;
    /** Activity结果启动器 */
    private ActivityResultLauncher<Intent> launcher;
    /** 开始/停止记录按钮 */
    private Button startRecord;
    /** 上次更新UI的时间戳，用于控制UI更新频率 */
    private long startTime = 0;

    /**
     * Activity创建时的初始化方法
     *
     * 执行流程：
     * 1. 设置布局文件
     * 2. 初始化UI组件
     * 3. 初始化数据解析线程
     * 4. 创建蓝牙管理器和设备适配器
     * 5. 启动蓝牙扫描规则设置
     */
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        // 初始化UI组件
        initView();
        // 初始化数据解析线程
        initRunnable();

        // 创建设备适配器
        DeviceAdapter adapter = new DeviceAdapter(this);
        // 启动数据解析线程
        new Thread(mParseRunnable).start();

        // 初始化蓝牙管理器
        ble = new MyBluetooth(this, adapter, mParseRunnable, mWaveForm);
        // 初始化设备选择对话框
        dialog = new MyDialog(this, ble, adapter);
        // 设置蓝牙扫描规则
        ble.scanRule();
    }

    /**
     * 初始化UI组件和相关工具
     *
     * 功能包括：
     * 1. 绑定所有UI控件
     * 2. 设置波形显示可见性
     * 3. 初始化文件操作工具
     * 4. 设置事件监听器
     * 5. 注册Activity结果启动器
     */
    private void initView() {
        // 绑定协议和频率相关控件
        protocolRG = findViewById(R.id.protocolRG);
        frequencyLayout = findViewById(R.id.frequency_layout);
        frequencyView = findViewById(R.id.frequency_view);
        packetFreq = findViewById(R.id.packetFreq);

        // 绑定设备信息显示控件
        name = findViewById(R.id.name);
        mac = findViewById(R.id.mac);
        hw = findViewById(R.id.hw);
        sw = findViewById(R.id.sw);

        // 绑定生理参数显示控件
        spo2Tv = findViewById(R.id.spo2Tv);
        prTv = findViewById(R.id.prTv);
        piTv = findViewById(R.id.piTv);
        rrTitle = findViewById(R.id.rr_title);
        rrTv = findViewById(R.id.rrTv);

        // 绑定波形显示控件并设置可见性
        mWaveForm = findViewById(R.id.wave_form);
        mWaveForm.setWaveformVisibility(true);

        // 绑定设备名称输入框
        inputDeviceName = findViewById(R.id.input_device_name);

        // 初始化文件操作工具
        myFiles = new MyFiles();
        // 设置事件监听器
        listener();

        // 注册Activity结果启动器（用于文件分享等操作）
        launcher = registerForActivityResult(new ActivityResultContracts.StartActivityForResult(), result -> {
            // 处理Activity返回结果（当前为空实现）
        });
    }

    /**
     * 设置所有UI控件的事件监听器
     *
     * 包括：
     * 1. 按钮点击事件监听
     * 2. 协议选择单选按钮组监听
     * 3. 频率选择单选按钮组监听
     */
    private void listener() {
        // 设置按钮点击监听器
        findViewById(R.id.search).setOnClickListener(this);        // 搜索设备按钮
        findViewById(R.id.confirm).setOnClickListener(this);       // 确认重命名按钮
        findViewById(R.id.hwBtn).setOnClickListener(this);         // 获取硬件版本按钮
        findViewById(R.id.swBtn).setOnClickListener(this);         // 获取软件版本按钮
        startRecord = findViewById(R.id.start_record);
        startRecord.setOnClickListener(this);                     // 开始/停止记录按钮
        findViewById(R.id.share).setOnClickListener(this);         // 分享数据按钮

        // 协议选择监听器
        protocolRG.setOnCheckedChangeListener((group, id) -> {
            if (id == R.id.bci_radio) {
                // 切换到BCI协议
                mHandler.sendEmptyMessage(0x02);
            } else if (id == R.id.bci_rr_radio) {
                // 切换到BCI-RESP协议（带呼吸率）
                mHandler.sendEmptyMessage(0x03);
            } else if (id == R.id.berry_radio) {
                // 切换到BERRY协议
                mHandler.sendEmptyMessage(0x04);
            }
        });

        // 频率选择监听器（仅BERRY协议可用）
        frequencyView.setOnCheckedChangeListener((group, id) -> {
            if (id == R.id.rb1) {
                ble.writeHex("0xF3");    // 设置频率模式1
            } else if (id == R.id.rb2) {
                ble.writeHex("0xF0");    // 设置频率模式2
            } else if (id == R.id.rb3) {
                ble.writeHex("0xF1");    // 设置频率模式3
            } else if (id == R.id.rb4) {
                ble.writeHex("0xF2");    // 设置频率模式4
            } else if (id == R.id.rb5) {
                ble.writeHex("0xF6");    // 设置频率模式5
            }
        });
    }

    /**
     * 初始化数据解析线程和回调监听器
     *
     * 创建ParseRunnable实例并设置数据变化监听器，用于：
     * 1. 接收设备信息（名称、MAC地址）
     * 2. 接收实时生理参数数据
     * 3. 接收设备版本信息
     * 4. 更新UI显示和保存数据到文件
     */
    private void initRunnable() {
        mParseRunnable = new ParseRunnable(new ParseRunnable.OnDataChangeListener() {
            /**
             * 设备信息回调
             * @param dName 设备名称
             * @param dMac 设备MAC地址
             */
            @Override
            public void deviceInfo(String dName, String dMac) {
                runOnUiThread(() -> {
                    name.setText(dName);    // 更新设备名称显示
                    mac.setText(dMac);      // 更新MAC地址显示
                });
            }

            /**
             * 实时数据回调
             * @param spo2 血氧饱和度 (0-100, 127表示无效值)
             * @param rr 呼吸率 (0表示无效值)
             * @param pr 心率 (0-255, 255表示无效值)
             * @param pi 灌注指数 (0表示无效值)
             * @param resp 呼吸相关参数
             * @param wave 波形数据点
             * @param pf 数据包频率 (-1表示无效值)
             */
            @Override
            public void value(int spo2, int rr, int pr, double pi, int resp, int wave, int pf) {
                runOnUiThread(() -> {
                    // 添加波形数据点
                    mWaveForm.addAmplitude(wave);

                    long currentTime = System.currentTimeMillis();
                    // 控制UI更新频率，每100ms更新一次
                    if (currentTime - startTime > 100) {
                        // 更新生理参数显示（无效值显示为"--"）
                        spo2Tv.setText(spo2 != 127 ? String.valueOf(spo2) : "--");
                        prTv.setText(pr != 255 ? String.valueOf(pr) : "--");
                        piTv.setText(pi != 0 ? String.valueOf(pi) : "--");
                        rrTv.setText(rr != 0 ? String.valueOf(rr) : "--");
                        packetFreq.setText(pf != -1 ? pf + "Hz" : "--");

                        // 保存数据到文件
                        myFiles.writeTxt(currentTime, rr, pr, spo2);
                        startTime = currentTime;
                    }
                });
            }

            /**
             * 硬件版本信息回调
             * @param v 硬件版本字符串
             */
            @Override
            public void hardwareVersion(String v) {
                runOnUiThread(() -> hw.setText(v));
            }

            /**
             * 软件版本信息回调
             * @param v 软件版本字符串
             */
            @Override
            public void softwareVersion(String v) {
                runOnUiThread(() -> sw.setText(v));
            }
        });
    }


    /**
     * Activity销毁时的清理工作
     *
     * 释放资源包括：
     * 1. 停止数据解析线程
     * 2. 注销Activity结果启动器
     * 3. 关闭文件操作
     */
    @Override
    protected void onDestroy() {
        super.onDestroy();
        // 停止数据解析线程
        if (mParseRunnable != null) mParseRunnable.setStop(false);
        // 注销Activity结果启动器
        if (launcher != null) launcher.unregister();
        // 关闭文件操作
        if (myFiles != null) myFiles.close();
    }

    /**
     * 统一的按钮点击事件处理方法
     *
     * @param v 被点击的视图
     */
    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.search) {
            // 搜索蓝牙设备
            startTime = System.currentTimeMillis();
            Permissions.all(this, ble, dialog);

        } else if (v.getId() == R.id.confirm) {
            // 确认设备重命名
            String n = inputDeviceName.getText().toString().trim();
            if (!TextUtils.isEmpty(n)) {
                ble.bleRename(n);
                // 延迟500ms后断开设备连接，提示用户重新连接
                mHandler.sendEmptyMessageDelayed(0x01, 500);
            } else {
                ToastUtil.showToastShort("Name is empty");
            }

        } else if (v.getId() == R.id.hwBtn) {
            // 获取硬件版本信息
            ble.writeHex("0xFE");

        } else if (v.getId() == R.id.swBtn) {
            // 获取软件版本信息
            ble.writeHex("0xFF");

        } else if (v.getId() == R.id.start_record) {
            // 开始/停止数据记录
            startTime = System.currentTimeMillis();
            Permissions.storage(this);  // 检查存储权限
            String name = startRecord.getText().toString().trim();
            if (ble.isConn()) {
                if (name.equals("Start")) {
                    mHandler.sendEmptyMessage(0x05);  // 开始记录
                } else {
                    mHandler.sendEmptyMessage(0x06);  // 停止记录
                }
            } else {
                mHandler.sendEmptyMessage(0x07);  // 设备未连接提示
            }

        } else if (v.getId() == R.id.share) {
            // 分享数据文件
            mHandler.sendEmptyMessageDelayed(0x08, 1000);
        }
    }

    /**
     * 主线程消息处理器
     * 用于处理各种UI更新和状态切换操作
     */
    Handler mHandler = new Handler(new Handler.Callback() {
        @SuppressLint("SetTextI18n")
        @Override
        public boolean handleMessage(@NonNull Message msg) {
            switch (msg.what) {
                case 0x01:
                    // 设备重命名后断开连接
                    ble.disconnectAllDevice();
                    name.setText("");
                    ToastUtil.showToastShort("Please reconnect the device!");
                    break;

                case 0x02:
                    // 切换到BCI协议模式
                    Model.MODEL = "BCI";
                    rrTitle.setText("Resp Rate");
                    frequencyLayout.setVisibility(View.GONE);  // 隐藏频率设置
                    ble.writeHex("0xE0");  // 发送BCI协议切换命令
                    break;

                case 0x03:
                    // 切换到BCI-RESP协议模式（带呼吸率）
                    Model.MODEL = "BCI-RESP";
                    rrTitle.setText("Resp Rate");
                    frequencyLayout.setVisibility(View.GONE);  // 隐藏频率设置
                    ble.writeHex("0xE0");  // 发送BCI协议切换命令
                    break;

                case 0x04:
                    // 切换到BERRY协议模式
                    Model.MODEL = "BERRY";
                    rrTitle.setText("RR");
                    frequencyLayout.setVisibility(View.VISIBLE);  // 显示频率设置
                    ble.writeHex("0xE1");  // 发送BERRY协议切换命令
                    break;

                case 0x05:
                    // 开始数据记录
                    myFiles.createTxt();
                    startRecord.setText("Stop");
                    break;

                case 0x06:
                    // 停止数据记录
                    startRecord.setText("Start");
                    myFiles.close();
                    break;

                case 0x07:
                    // 设备未连接提示
                    ToastUtil.showToastShort("Please connect the device!");
                    break;

                case 0x08:
                    // 分享数据文件
                    shareFile();
                    break;
            }
            return false;
        }
    });

    /**
     * 获取文件的FileProvider URI
     *
     * @param path 文件路径
     * @return 文件的URI，如果文件不存在或为空则返回null
     */
    private Uri fpUri(String path) {
        File file = new File(path);
        if (file.exists() && file.length() > 0) {
            // 使用FileProvider生成安全的文件URI
            return FileProvider.getUriForFile(this, getPackageName() + ".fileProvider", file);
        }
        return null;
    }

    /**
     * 分享数据文件
     *
     * 功能：
     * 1. 获取数据文件的URI
     * 2. 创建分享Intent
     * 3. 设置必要的权限
     * 4. 启动分享选择器
     */
    private void shareFile() {
        Uri uri = fpUri(myFiles.getFilePath());
        if (uri != null) {
            // 创建分享Intent
            Intent intent = new Intent(Intent.ACTION_SEND);
            intent.setType("application/octet-stream");  // 设置文件类型
            intent.putExtra(Intent.EXTRA_EMAIL, new String[]{});
            intent.putExtra(Intent.EXTRA_SUBJECT, "Data");
            intent.putExtra(Intent.EXTRA_TEXT, "Content: ");
            intent.putExtra(Intent.EXTRA_STREAM, uri);  // 附加文件

            // 添加URI权限标志
            intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION | Intent.FLAG_GRANT_WRITE_URI_PERMISSION);

            // 创建分享选择器
            Intent chooser = Intent.createChooser(intent, "Share");

            // 为所有可能的接收应用授予URI权限
            @SuppressLint("QueryPermissionsNeeded")
            List<ResolveInfo> resInfoList = getPackageManager().queryIntentActivities(chooser, PackageManager.MATCH_DEFAULT_ONLY);
            for (ResolveInfo resolveInfo : resInfoList) {
                String packageName = resolveInfo.activityInfo.packageName;
                grantUriPermission(packageName, uri, Intent.FLAG_GRANT_WRITE_URI_PERMISSION | Intent.FLAG_GRANT_READ_URI_PERMISSION);
            }

            // 启动分享选择器
            launcher.launch(Intent.createChooser(chooser, "Share"));
        } else {
            // 没有数据文件可分享
            ToastUtil.showToastShort("No Data");
        }
    }
}