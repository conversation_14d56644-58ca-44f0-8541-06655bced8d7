package com.berry_med.bci.blutooth;

/**
 * 蓝牙设备模型配置类
 *
 * 定义了SpO2血氧仪设备的蓝牙通信相关常量和配置信息
 * 包括：
 * 1. 蓝牙服务UUID
 * 2. 特征值UUID（发送、接收、重命名）
 * 3. 当前设备协议模型
 *
 * @description Device Model
 * <AUTHOR>
 * @date 2024/10/23 9:47
 */
public class Model {
    /** 蓝牙服务UUID - 用于识别SpO2设备的主要服务 */
    public static final String UUID_SERVICE_DATA           = "49535343-fe7d-4ae5-8fa9-9fafd205e455";

    /** 发送数据特征值UUID - 用于向设备发送命令和配置 */
    public static final String CHARACTERISTIC_UUID_SEND    = "49535343-1e4d-4bd9-ba61-23c647249616";

    /** 接收数据特征值UUID - 用于接收设备返回的数据 */
    public static final String CHARACTERISTIC_UUID_RECEIVE = "*************-43f4-a8d4-ecbe34729bb3";

    /** 设备重命名特征值UUID - 用于修改设备名称 */
    public static final String CHARACTERISTIC_UUID_RENAME  = "00005343-0000-1000-8000-00805f9b34fb";

    /**
     * 当前设备协议模型
     * 可选值：
     * - "BCI": 基础BCI协议
     * - "BCI-RESP": 带呼吸率的BCI协议
     * - "BERRY": BERRY协议（支持频率调节）
     */
    public static String MODEL = "BCI";
}
