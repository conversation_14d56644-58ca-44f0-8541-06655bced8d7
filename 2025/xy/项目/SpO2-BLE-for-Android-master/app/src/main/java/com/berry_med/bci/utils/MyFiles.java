package com.berry_med.bci.utils;

import android.os.Build;
import android.os.Environment;

import com.berry_med.bci.application.MyApplication;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStreamWriter;
import java.nio.charset.StandardCharsets;

/**
 * 文件操作工具类
 *
 * 主要功能：
 * 1. 创建和管理数据记录文件
 * 2. 写入SpO2测量数据到CSV格式文件
 * 3. 处理不同Android版本的存储路径
 * 4. 提供文件路径获取功能
 *
 * 数据格式：
 * - CSV格式：Time,RR,Pr,SpO₂
 * - 时间戳为毫秒级
 * - 支持UTF-8编码
 *
 * @Description File
 * <AUTHOR>
 * @Date 2025/3/30 11:26
 */
public class MyFiles {
    /** 当前文件路径 */
    private String filePath = "";
    /** 文件写入器 */
    private BufferedWriter writer;

    /**
     * 获取内部存储卡路径
     * 兼容Android 10及以上版本的分区存储
     *
     * @return 存储路径字符串
     */
    private String getInnerSDCardPath() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            // Android 10及以上版本使用应用专用目录
            File file = MyApplication.getContext().getExternalFilesDir(Environment.DIRECTORY_DOCUMENTS);
            if (file == null) return "";
            return file.getAbsolutePath() + File.separator + "Spo2BleForAndroid";
        }
        // Android 10以下版本使用外部存储根目录
        return Environment.getExternalStorageDirectory().getPath();
    }

    /**
     * 创建数据记录文件
     *
     * 功能：
     * 1. 创建存储目录（如果不存在）
     * 2. 创建TestRecord.txt文件
     * 3. 写入CSV文件头
     * 4. 初始化文件写入器
     */
    public void createTxt() {
        File file = new File(getInnerSDCardPath());
        if (!file.exists()) file.mkdirs();
        filePath = file.getPath() + File.separator + "TestRecord.txt";
        try {
            FileOutputStream outputStream = new FileOutputStream(filePath, false);
            OutputStreamWriter outputStreamWriter = new OutputStreamWriter(outputStream, StandardCharsets.UTF_8);
            writer = new BufferedWriter(outputStreamWriter);
            // 写入CSV文件头
            writer.write("Time,RR,Pr,SpO₂\r\n");
            writer.flush();
        } catch (Exception e) {
            //noinspection CallToPrintStackTrace
            e.printStackTrace();
        }
    }

    /**
     * 写入测量数据到文件
     *
     * @param time 时间戳（毫秒）
     * @param rr 呼吸率
     * @param pr 心率
     * @param spo2 血氧饱和度
     */
    public void writeTxt(long time, int rr, int pr, int spo2) {
        try {
            if (writer != null) {
                // 写入CSV格式数据行
                writer.write(time + "," + rr + "," + pr + "," + spo2 + "\r\n");
                writer.flush();
            }
        } catch (Exception e) {
            //noinspection CallToPrintStackTrace
            e.printStackTrace();
        }
    }

    /**
     * 关闭文件写入器
     * 释放文件资源
     */
    public void close() {
        try {
            if (writer != null) writer.close();
        } catch (Exception e) {
            //noinspection CallToPrintStackTrace
            e.printStackTrace();
        }
    }

    /**
     * 获取当前文件路径
     * @return 文件完整路径
     */
    public String getFilePath() {
        return filePath;
    }
}
