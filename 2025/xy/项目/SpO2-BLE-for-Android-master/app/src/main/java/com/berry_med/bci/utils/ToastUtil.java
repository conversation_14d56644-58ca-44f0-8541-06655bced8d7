package com.berry_med.bci.utils;

import android.widget.Toast;

import com.berry_med.bci.application.MyApplication;

/**
 * Toast提示工具类
 *
 * 主要功能：
 * 1. 提供全局Toast显示功能
 * 2. 避免重复创建Toast实例
 * 3. 统一管理应用内的提示信息
 *
 * 特性：
 * - 单例模式，避免Toast重叠
 * - 自动使用应用全局Context
 * - 支持动态更新Toast内容
 *
 * @description Toast
 * <AUTHOR>
 * @date 2024/10/23 9:49
 */
public class ToastUtil {
    /** Toast实例，采用单例模式 */
    private static Toast toast;

    /**
     * 显示短时间Toast提示
     *
     * @param content 要显示的提示内容
     */
    public static void showToastShort(String content) {
        if (toast == null) {
            // 首次创建Toast实例
            toast = Toast.makeText(MyApplication.getContext(), content, Toast.LENGTH_SHORT);
        } else {
            // 更新已存在Toast的内容
            toast.setText(content);
        }
        toast.show();
    }
}
