package com.berry_med.bci.blutooth;

import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 蓝牙数据解析线程类
 *
 * 主要功能：
 * 1. 接收来自蓝牙设备的原始数据
 * 2. 按照协议解析数据包
 * 3. 提取生理参数（SpO2、心率、灌注指数、呼吸率等）
 * 4. 通过回调接口通知UI更新
 * 5. 处理设备版本信息
 *
 * 支持的协议：
 * - BCI协议
 * - BCI-RESP协议（带呼吸率）
 * - BERRY协议
 *
 * @description ParseRunnable
 * <AUTHOR>
 * @date 2024/10/23 10:29
 */
public class ParseRunnable implements Runnable {
    /** 蓝牙数据缓冲队列，容量256 */
    protected LinkedBlockingQueue<Integer> oxiData = new LinkedBlockingQueue<>(256);
    /** 数据包解析缓冲区，固定20字节 */
    protected int[] parseBuf = new int[20];
    /** 数据变化监听器，用于回调UI更新 */
    protected OnDataChangeListener mOnDataChangeListener;

    /** 当前设备协议模型 */
    protected String currentModel = "";
    /** 数据缓冲数组 */
    private List<Integer> buffArray;

    /** 线程停止标志 */
    boolean isStop = false;

    /**
     * 构造函数
     * @param onDataChangeListener 数据变化监听器
     */
    public ParseRunnable(OnDataChangeListener onDataChangeListener) {
        isStop = true;
        this.mOnDataChangeListener = onDataChangeListener;
    }

    /**
     * 设置线程停止状态
     * @param stop true表示继续运行，false表示停止
     */
    public void setStop(boolean stop) {
        isStop = stop;
    }

    /**
     * 初始化缓冲数组
     */
    public void init() {
        buffArray = new ArrayList<>();
    }

    /**
     * 数据变化监听接口
     * 用于通知UI层数据更新
     */
    public interface OnDataChangeListener {
        /**
         * 设备信息回调
         * @param name 设备名称
         * @param mac 设备MAC地址
         */
        void deviceInfo(String name, String mac);

        /**
         * 实时数据回调
         * @param spo2 血氧饱和度
         * @param rr 呼吸率
         * @param pr 心率
         * @param pi 灌注指数
         * @param resp 呼吸相关参数
         * @param wave 波形数据
         * @param packetFreq 数据包频率
         */
        void value(int spo2, int rr, int pr, double pi, int resp, int wave, int packetFreq);

        /**
         * 硬件版本信息回调
         * @param v 硬件版本字符串
         */
        void hardwareVersion(String v);

        /**
         * 软件版本信息回调
         * @param v 软件版本字符串
         */
        void softwareVersion(String v);
    }

    /**
     * 添加来自蓝牙设备的数据到解析队列
     *
     * 功能：
     * 1. 检测版本信息（HV/SV）并直接回调
     * 2. 将普通数据添加到解析队列
     *
     * @param data 来自蓝牙设备的原始数据
     */
    public void add(byte[] data) {
        try {
            if (data != null) {
                // 转换为ASCII字符串以检测版本信息
                String ascii = convertToAscii(data);
                if (ascii.contains("SV")) {
                    // 软件版本信息
                    mOnDataChangeListener.softwareVersion(getHvOrSv(data, "SV"));
                } else if (ascii.contains("HV")) {
                    // 硬件版本信息
                    mOnDataChangeListener.hardwareVersion(getHvOrSv(data, "HV"));
                } else {
                    // 普通数据，添加到解析队列
                    if (data.length >= 2) {
                        for (byte d : data) oxiData.put(toUnsignedInt(d));
                    }
                }
            }
        } catch (Exception e) {
            //noinspection CallToPrintStackTrace
            e.printStackTrace();
        }
    }

    /**
     * 将字节数组转换为ASCII字符串
     * @param array 字节数组
     * @return ASCII字符串
     */
    private String convertToAscii(byte[] array) {
        StringBuilder sb = new StringBuilder();
        for (int i : array) {
            sb.append((char) (i & 0xFF));
        }
        return sb.toString();
    }

    /**
     * 从数据中提取硬件版本或软件版本信息
     * @param data 原始数据
     * @param ver 版本类型（"HV"或"SV"）
     * @return 版本字符串
     */
    private String getHvOrSv(byte[] data, String ver) {
        // 去除数据包头尾，提取版本信息部分
        byte[] res = Arrays.copyOfRange(data, 2, data.length - 1);
        Matcher matcher = Pattern.compile(ver + "[^?\\x00]+").matcher(convertToAscii(res));
        if (matcher.find() && matcher.group().contains(".")) {
            return matcher.group();
        }
        return "";
    }

    /**
     * Parsing the protocol as the manual.
     */
    @Override
    public void run() {
        int PACKAGE_LEN = 20;
        while (isStop) {
            int data = getData();
            if (Model.MODEL.equals("BERRY")) {
                parseBuf[0] = data;
                for (int i = 1; i < PACKAGE_LEN; i++) {
                    parseBuf[i] = getData();
                }
                berryParse(parseBuf);
            } else {
                if ((data & 0x80) > 0) {
                    parseBuf[0] = data;
                    for (int i = 1; i < PACKAGE_LEN; i++) {
                        data = getData();
                        if ((data & 0x80) == 0) parseBuf[i] = data;
                    }
                    bciParse(parseBuf);
                }
            }
        }
    }

    // BCI
    private void bciParse(int[] data) {
        int beep = data[0];
        int wave = data[1];
        int spo2 = data[4];
        int pr = data[3] | ((data[2] & 0x40) << 1);
        int resp = 0;
        double pi = 0.0;
        if (Model.MODEL.equals("BCI-RESP")) {
            pi = _calculatePi(beep, data[2]);
            resp = data[6];
        } else {
            pi = data[0] & 0x0F;
        }
        mOnDataChangeListener.value(spo2, 0, pr, pi, resp, wave, -1);
    }

    public void berryParse(int[] array) {
        // Convert int[] to List<Integer> and add to buffArray
        for (int value : array) {
            buffArray.add(value);
        }

        int i = 0; // Current Index
        int validIndex = 0; // Valid Index
        int maxIndex = buffArray.size() - 20; // Data Space

        while (i <= maxIndex) {
            // Failed to match the headers
            if (buffArray.get(i) != 0xFF || buffArray.get(i + 1) != 0xAA) {
                i += 1;
                validIndex = i;
                continue;
            }

            // The header is successfully matched
            int total = 0;
            int checkSum = buffArray.get(i + 19);
            for (int index = 0; index <= 18; index++) {
                total += buffArray.get(i + index);
            }

            // If the verification fails, discard the two data
            if (checkSum != (total % 256)) {
                i += 2;
                validIndex = i;
                continue;
            }

            // Extracting a sublist for data
            List<Integer> data = buffArray.subList(i, i + 19);

            if (data.size() >= 19) {
                int spo2 = data.get(4);
                int pr = data.get(6);
                int rr = (data.get(8) + (data.get(9) << 8)) * 5; // 200Hz
                double pi = data.get(10) / 10.0;
                int wave = data.get(12);
                int packetFreq = data.get(18);

                mOnDataChangeListener.value(spo2, rr, pr, pi, 0, wave, packetFreq);
            }
            i += 20; // Move back one group
            validIndex = i;
        }

        // Update buffArray to retain only valid data
        buffArray = new ArrayList<>(buffArray.subList(validIndex, buffArray.size()));
    }

    private int toUnsignedInt(byte x) {
        return ((int) x) & 0xFF;
    }

    private int getData() {
        try {
            return oxiData.take();
        } catch (Exception e) {
            //noinspection CallToPrintStackTrace
            e.printStackTrace();
        }
        return 0;
    }

    private double _calculatePi(int lower, int higher) {
        DecimalFormat df = new DecimalFormat("#.00");
        String value = df.format(((lower & 0x0F) + (higher & 0x0F) * 16) / 10.0);
        return Double.parseDouble(value);
    }

    public OnDataChangeListener getOnDataChangeListener() {
        return mOnDataChangeListener;
    }
}
