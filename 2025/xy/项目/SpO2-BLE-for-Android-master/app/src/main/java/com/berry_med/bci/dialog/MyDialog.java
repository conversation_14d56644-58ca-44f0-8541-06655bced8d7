package com.berry_med.bci.dialog;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.util.DisplayMetrics;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageButton;
import android.widget.ListView;

import com.berry_med.bci.R;
import com.berry_med.bci.blutooth.MyBluetooth;
import com.clj.fastble.data.BleDevice;

/**
 * 设备选择对话框类
 *
 * 主要功能：
 * 1. 显示可用的蓝牙设备列表
 * 2. 提供设备搜索和刷新功能
 * 3. 处理设备选择和连接操作
 * 4. 管理对话框的显示和隐藏
 *
 * UI组件：
 * - ListView：显示设备列表
 * - 关闭按钮：关闭对话框
 * - 刷新按钮：重新扫描设备
 *
 * @description Dialog
 * <AUTHOR>
 * @date 2024/10/23 10:03
 */
public class MyDialog {
    /** 上下文对象 */
    protected Context context;
    /** 对话框实例 */
    protected Dialog dialog;

    /** 设备列表适配器 */
    private final DeviceAdapter adapter;
    /** 蓝牙管理器 */
    private final MyBluetooth ble;

    /**
     * 构造函数
     *
     * @param context 上下文
     * @param ble 蓝牙管理器
     * @param adapter 设备适配器
     */
    public MyDialog(Context context, MyBluetooth ble, DeviceAdapter adapter) {
        this.context = context;
        this.adapter = adapter;
        this.ble = ble;
        // 创建自定义样式的对话框
        dialog = new Dialog(context, R.style.my_dialog);
    }

    public void show() {
        dialog.setContentView(R.layout.dialog_device_list);
        dialog.setCancelable(false);
        event(dialog);
        dialog.show();
        size(dialog);
    }

    private void event(Dialog dialog) {
        ble.scan();
        ListView listView = dialog.findViewById(R.id.list_view);
        ImageButton close = dialog.findViewById(R.id.close);
        ImageButton refresh = dialog.findViewById(R.id.refresh);
        close.setOnClickListener(v -> dismiss());
        refresh.setOnClickListener(v -> ble.scan());
        listView.setSelector(R.color.transparent);
        listView.setAdapter(adapter);
        adapter.notifyDataSetChanged();

        listView.setOnItemClickListener((parent, view, position, id) -> {
            BleDevice bleDevice = adapter.getDevices().get(position);
            if (bleDevice != null) {
                ble.conn(bleDevice);
                dismiss();
            }
        });
    }

    public void dismiss() {
        dialog.dismiss();
    }

    private void size(Dialog dialog) {
        Window window = dialog.getWindow();
        if (window != null) {
            WindowManager.LayoutParams lp = window.getAttributes();
            lp.width = width();
            lp.height = height();
            window.setAttributes(lp);
        }
    }

    private int width() {
        DisplayMetrics displayMetrics = new DisplayMetrics();
        ((Activity) context).getWindowManager().getDefaultDisplay().getMetrics(displayMetrics);
        return (int) (displayMetrics.widthPixels * 0.9f);
    }

    private int height() {
        DisplayMetrics displayMetrics = new DisplayMetrics();
        ((Activity) context).getWindowManager().getDefaultDisplay().getMetrics(displayMetrics);
        return (int) (displayMetrics.heightPixels * 0.6f);
    }
}
