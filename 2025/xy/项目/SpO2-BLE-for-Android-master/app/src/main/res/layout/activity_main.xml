<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:paddingStart="10dp"
    android:paddingEnd="10dp"
    tools:context=".MainActivity">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        tools:ignore="UselessParent">

        <Button
            android:id="@+id/search"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:layout_centerVertical="true"
            android:text="Search"
            android:textAllCaps="false"
            android:textSize="13sp"
            tools:ignore="HardcodedText" />

        <RadioGroup
            android:id="@+id/protocolRG"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:orientation="horizontal"
            tools:ignore="UselessParent">

            <RadioButton
                android:id="@+id/bci_radio"
                android:layout_width="wrap_content"
                android:layout_height="50dp"
                android:button="@null"
                android:checked="true"
                android:drawableTop="?android:attr/listChoiceIndicatorSingle"
                android:gravity="center_horizontal"
                android:scaleX="0.8"
                android:scaleY="0.8"
                android:text="BCI"
                android:textSize="13sp"
                tools:ignore="HardcodedText" />

            <View
                android:layout_width="10dp"
                android:layout_height="10dp" />


            <RadioButton
                android:id="@+id/bci_rr_radio"
                android:layout_width="wrap_content"
                android:layout_height="50dp"
                android:button="@null"
                android:drawableTop="?android:attr/listChoiceIndicatorSingle"
                android:gravity="center_horizontal"
                android:scaleX="0.8"
                android:scaleY="0.8"
                android:text="BCI RESP"
                android:textSize="13sp"
                tools:ignore="HardcodedText" />

            <View
                android:layout_width="10dp"
                android:layout_height="10dp" />

            <RadioButton
                android:id="@+id/berry_radio"
                android:layout_width="wrap_content"
                android:layout_height="50dp"
                android:button="@null"
                android:drawableTop="?android:attr/listChoiceIndicatorSingle"
                android:gravity="center_horizontal"
                android:scaleX="0.8"
                android:scaleY="0.8"
                android:text="BERRY"
                android:textSize="13sp"
                tools:ignore="HardcodedText" />

        </RadioGroup>
    </RelativeLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginBottom="5dp"
        android:background="@color/berry_med" />

    <LinearLayout
        android:id="@+id/frequency_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:visibility="gone">

        <RadioGroup
            android:id="@+id/frequency_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal">

            <RadioButton
                android:id="@+id/rb1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:button="@null"
                android:drawableTop="?android:attr/listChoiceIndicatorSingle"
                android:gravity="center_horizontal"
                android:padding="0dp"
                android:scaleX="0.7"
                android:scaleY="0.7"
                android:text="1Hz"
                android:textSize="13sp"
                tools:ignore="HardcodedText" />

            <RadioButton
                android:id="@+id/rb2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:button="@null"
                android:drawableTop="?android:attr/listChoiceIndicatorSingle"
                android:gravity="center_horizontal"
                android:padding="0dp"
                android:scaleX="0.7"
                android:scaleY="0.7"
                android:text="50Hz"
                android:textSize="13sp"
                tools:ignore="HardcodedText" />

            <RadioButton
                android:id="@+id/rb3"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:button="@null"
                android:drawableTop="?android:attr/listChoiceIndicatorSingle"
                android:gravity="center_horizontal"
                android:padding="0dp"
                android:scaleX="0.8"
                android:scaleY="0.8"
                android:text="100Hz"
                android:textSize="13sp"
                tools:ignore="HardcodedText" />

            <RadioButton
                android:id="@+id/rb4"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:button="@null"
                android:drawableTop="?android:attr/listChoiceIndicatorSingle"
                android:gravity="center_horizontal"
                android:padding="0dp"
                android:scaleX="0.7"
                android:scaleY="0.7"
                android:text="200Hz"
                android:textSize="13sp"
                tools:ignore="HardcodedText" />

            <RadioButton
                android:id="@+id/rb5"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:button="@null"
                android:drawableTop="?android:attr/listChoiceIndicatorSingle"
                android:gravity="center_horizontal"
                android:padding="0dp"
                android:scaleX="0.7"
                android:scaleY="0.7"
                android:text="STOP"
                android:textSize="13sp"
                tools:ignore="HardcodedText" />

        </RadioGroup>


        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:text="Packet Frequency"
                android:textSize="13sp"
                tools:ignore="HardcodedText" />

            <TextView
                android:id="@+id/packetFreq"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:text="--"
                android:textSize="13sp"
                tools:ignore="HardcodedText" />
        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginTop="5dp"
            android:layout_marginBottom="5dp"
            android:background="@color/berry_med" />

    </LinearLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:text="Name"
            tools:ignore="HardcodedText" />

        <TextView
            android:id="@+id/name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:text="--"
            tools:ignore="HardcodedText" />
    </RelativeLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:text="ID"
            tools:ignore="HardcodedText" />

        <TextView
            android:id="@+id/mac"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:text="--"
            tools:ignore="HardcodedText" />
    </RelativeLayout>


    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp">

        <TextView
            android:id="@+id/hwBtn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/round_btn_background"
            android:paddingLeft="15dp"
            android:paddingTop="3dp"
            android:paddingRight="15dp"
            android:paddingBottom="3dp"
            android:text="HV"
            android:textColor="@color/white"
            android:textSize="11sp"
            tools:ignore="HardcodedText" />

        <TextView
            android:id="@+id/hw"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:text="--"
            tools:ignore="HardcodedText" />
    </RelativeLayout>


    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp">

        <TextView
            android:id="@+id/swBtn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/round_btn_background"
            android:paddingLeft="15dp"
            android:paddingTop="3dp"
            android:paddingRight="15dp"
            android:paddingBottom="3dp"
            android:text="SV"
            android:textColor="@color/white"
            android:textSize="11sp"
            tools:ignore="HardcodedText" />

        <TextView
            android:id="@+id/sw"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:text="--"
            tools:ignore="HardcodedText" />
    </RelativeLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="5dp"
        android:background="@color/berry_med" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="75dp"
        android:layout_marginTop="5dp"
        android:gravity="center"
        android:orientation="horizontal">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/round_border">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="3dp"
                android:layout_marginTop="1dp"
                android:text="SpO₂"
                android:textSize="13sp"
                tools:ignore="HardcodedText" />

            <TextView
                android:id="@+id/spo2Tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="--"
                android:textSize="15sp"
                tools:ignore="HardcodedText" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_alignParentBottom="true"
                android:layout_marginEnd="3dp"
                android:layout_marginBottom="1dp"
                android:text="%"
                android:textSize="13sp"
                tools:ignore="HardcodedText" />
        </RelativeLayout>

        <View
            android:layout_width="10dp"
            android:layout_height="10dp" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/round_border">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="3dp"
                android:layout_marginTop="1dp"
                android:text="PR"
                android:textSize="13sp"
                tools:ignore="HardcodedText" />

            <TextView
                android:id="@+id/prTv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="--"
                android:textSize="15sp"
                tools:ignore="HardcodedText" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_alignParentBottom="true"
                android:layout_marginEnd="3dp"
                android:layout_marginBottom="1dp"
                android:text="bpm"
                android:textSize="13sp"
                tools:ignore="HardcodedText" />
        </RelativeLayout>

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="75dp"
        android:layout_marginTop="10dp"
        android:gravity="center"
        android:orientation="horizontal">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/round_border">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="3dp"
                android:layout_marginTop="1dp"
                android:text="PI"
                android:textSize="13sp"
                tools:ignore="HardcodedText" />

            <TextView
                android:id="@+id/piTv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="--"
                android:textSize="15sp"
                tools:ignore="HardcodedText" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_alignParentBottom="true"
                android:layout_marginEnd="5dp"
                android:layout_marginBottom="5dp"
                android:text=""
                android:textSize="13sp"
                tools:ignore="HardcodedText" />
        </RelativeLayout>

        <View
            android:layout_width="10dp"
            android:layout_height="10dp" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/round_border">

            <TextView
                android:id="@+id/rr_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="3dp"
                android:layout_marginTop="1dp"
                android:text="Resp"
                android:textSize="13sp"
                tools:ignore="HardcodedText" />

            <TextView
                android:id="@+id/rrTv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="--"
                android:textSize="15sp"
                tools:ignore="HardcodedText" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_alignParentBottom="true"
                android:layout_marginEnd="5dp"
                android:layout_marginBottom="5dp"
                android:text=""
                android:textSize="13sp"
                tools:ignore="HardcodedText,RelativeOverlap" />
        </RelativeLayout>

    </LinearLayout>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="left|center_vertical"
        android:layout_marginTop="5dp"
        android:text="PR Waveform"
        android:textSize="13sp"
        android:textStyle="bold"
        tools:ignore="HardcodedText,RtlHardcoded" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="100dp"
        android:layout_marginTop="5dp"
        android:background="@drawable/round_background_wbg"
        android:padding="3dp">

        <com.berry_med.bci.blutooth.WaveForm
            android:id="@+id/wave_form"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="5dp"
        android:background="@color/berry_med" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:text="Record &amp; Share"
            android:textSize="13sp"
            tools:ignore="HardcodedText" />

        <Button
            android:id="@+id/share"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:text="Share"
            android:textSize="13sp"
            tools:ignore="HardcodedText" />

        <Button
            android:id="@+id/start_record"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:layout_centerVertical="true"
            android:layout_marginEnd="5dp"
            android:layout_toStartOf="@id/share"
            android:text="Start"
            android:textSize="13sp"
            tools:ignore="HardcodedText" />

    </RelativeLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="5dp"
        android:layout_marginBottom="5dp"
        android:background="@color/berry_med" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="left|center_vertical"
        android:text="Set Bluetooth Name"
        android:textSize="13sp"
        android:textStyle="bold"
        tools:ignore="HardcodedText,RtlHardcoded" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center">

        <EditText
            android:id="@+id/input_device_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:hint="Enter device name"
            android:textSize="13sp"
            tools:ignore="Autofill,HardcodedText,TextFields" />

        <View
            android:layout_width="10dp"
            android:layout_height="10dp" />

        <Button
            android:id="@+id/confirm"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_weight="2"
            android:text="Confirm"
            android:textAllCaps="false"
            android:textSize="13sp"
            tools:ignore="HardcodedText" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="bottom|center_horizontal"
        android:paddingBottom="10dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="v1.0.0"
            android:textColor="#BEBEBE"
            tools:ignore="HardcodedText" />
    </LinearLayout>
</LinearLayout>