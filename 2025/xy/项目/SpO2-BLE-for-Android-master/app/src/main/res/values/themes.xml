<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Base.Theme.SpO2BLEforAndroid" parent="Theme.Material3.DayNight.NoActionBar">
        <!-- Customize your light theme here. -->
        <!-- <item name="colorPrimary">@color/my_light_primary</item> -->
        <item name="colorPrimary">@color/berry_med</item>
        <item name="colorPrimaryVariant">@color/berry_med</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorSecondary">@color/berry_med</item>
        <item name="colorSecondaryVariant">@color/berry_med</item>
        <item name="colorOnSecondary">@color/black</item>
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
    </style>

    <style name="Theme.SpO2BLEforAndroid" parent="Base.Theme.SpO2BLEforAndroid" />
    <!--   progress dialog  -->
    <style name="my_dialog" parent="@android:style/Theme.Dialog">
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowIsTranslucent">false</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:background">@null</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <!-- Dialog背景不变暗 -->
        <item name="android:backgroundDimEnabled">true</item>
    </style>
</resources>