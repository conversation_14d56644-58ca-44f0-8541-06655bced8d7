<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">


    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="5dp"
        android:paddingTop="10dp"
        android:paddingBottom="10dp"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="RtlSymmetry">

        <LinearLayout
            android:id="@+id/ll"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="vertical"
            android:paddingStart="5dp">

            <TextView
                android:id="@+id/name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text=""
                android:textColor="@color/black"
                android:textSize="15sp" />

            <TextView
                android:id="@+id/mac"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:text=""
                android:textColor="@color/black"
                android:textSize="15sp" />
        </LinearLayout>

        <TextView
            android:id="@+id/rssi"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_marginStart="10dp"
            android:layout_toEndOf="@id/ll"
            android:text=""
            android:textColor="@color/black"
            android:textSize="15sp" />
    </RelativeLayout>


</androidx.constraintlayout.widget.ConstraintLayout>