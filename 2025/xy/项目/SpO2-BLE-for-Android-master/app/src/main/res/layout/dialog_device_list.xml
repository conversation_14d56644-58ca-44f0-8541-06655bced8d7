<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/round_background">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/round_background"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="55dp"
            android:background="@drawable/round_background_device">

            <ImageButton
                android:id="@+id/close"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginStart="10dp"
                android:background="@android:color/transparent"
                android:scaleX="0.8"
                android:scaleY="0.8"
                android:src="@mipmap/round_close"
                tools:ignore="ContentDescription" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="Bluetooth Devices"
                android:textColor="@color/white"
                android:textSize="15sp"
                tools:ignore="HardcodedText" />


            <ImageButton
                android:id="@+id/refresh"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="10dp"
                android:background="@android:color/transparent"
                android:scaleX="0.8"
                android:scaleY="0.8"
                android:src="@mipmap/refresh"
                tools:ignore="ContentDescription" />

        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginBottom="10dp"
            android:background="@color/black" />

        <ListView
            android:id="@+id/list_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp" />

    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>