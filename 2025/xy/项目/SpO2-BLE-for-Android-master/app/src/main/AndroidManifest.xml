<?xml version="1.0" encoding="utf-8"?>
<!--
    SpO2血氧仪蓝牙连接应用 - Android清单文件

    主要配置：
    1. 蓝牙相关权限（扫描、连接、管理）
    2. 位置权限（蓝牙扫描需要）
    3. 存储权限（数据文件保存）
    4. 硬件特性要求（蓝牙低功耗）
    5. 应用组件配置
-->
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- 网络权限 - 用于可能的网络通信 -->
    <uses-permission android:name="android.permission.INTERNET" />

    <!-- 传统蓝牙权限 - 兼容旧版本Android -->
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />

    <!-- 位置权限 - 蓝牙扫描必需 -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />

    <!-- Android 12+ 新蓝牙权限 -->
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADVERTISE" />
    <uses-permission
        android:name="android.permission.BLUETOOTH_PRIVILEGED"
        tools:ignore="ProtectedPermissions" />

    <!-- 存储权限 - 数据文件读写 -->
    <uses-permission
        android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        tools:ignore="ScopedStorage" />

    <!-- 硬件特性要求 - 蓝牙低功耗支持 -->
    <uses-feature
        android:name="android.hardware.bluetooth_le"
        android:required="true" />

    <!-- 应用程序配置 -->
    <application
        android:name=".application.MyApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:preserveLegacyExternalStorage="true"
        android:requestLegacyExternalStorage="true"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.SpO2BLEforAndroid"
        android:usesCleartextTraffic="true"
        tools:targetApi="31">

        <!-- 主活动 - 应用入口点 -->
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:screenOrientation="portrait"
            tools:ignore="DiscouragedApi,LockedOrientationActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- 文件提供者 - 用于安全地分享应用内文件 -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="com.berry_med.bci.fileProvider"
            android:exported="false"
            android:grantUriPermissions="true">

            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />

        </provider>

        <!-- 分区存储配置 -->
        <meta-data
            android:name="ScopedStorage"
            android:value="true" />
    </application>

</manifest>