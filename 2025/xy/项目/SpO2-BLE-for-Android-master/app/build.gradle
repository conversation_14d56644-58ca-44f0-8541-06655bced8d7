/*
 * SpO2血氧仪蓝牙连接应用 - Gradle构建配置文件
 *
 * 主要配置：
 * 1. Android应用基本信息（包名、版本等）
 * 2. 编译和目标SDK版本
 * 3. 构建类型配置
 * 4. 依赖库管理
 * 5. 兼容性设置
 */

plugins {
    alias(libs.plugins.android.application)
}

android {
    // 应用包名空间
    namespace 'com.berry_med.bci'
    // 编译SDK版本
    compileSdk 34
    // 构建工具版本
    buildToolsVersion "34.0.0"

    defaultConfig {
        // 应用唯一标识符
        applicationId "com.berry_med.bci"
        // 最低支持的Android版本（Android 8.0）
        minSdk 26
        // 目标Android版本
        targetSdk 34
        // 版本号（用于应用商店版本管理）
        versionCode 1
        // 版本名称（用户可见）
        versionName "1.0"

        // 测试运行器配置
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    // 构建类型配置
    buildTypes {
        release {
            // 发布版本不启用代码混淆（可根据需要开启）
            minifyEnabled false
            // ProGuard配置文件
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    // Java编译选项
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    // 解决JDK兼容性问题的打包选项
    packagingOptions {
        pickFirst '**/libc++_shared.so'
        pickFirst '**/libjsc.so'
    }
}

// 项目依赖配置
dependencies {
    // Android核心库
    implementation libs.appcompat          // Android兼容库
    implementation libs.material           // Material Design组件
    implementation libs.activity           // Activity相关功能
    implementation libs.constraintlayout   // 约束布局

    // 测试相关依赖
    testImplementation libs.junit                    // 单元测试框架
    androidTestImplementation libs.ext.junit        // Android测试扩展
    androidTestImplementation libs.espresso.core    // UI测试框架

    // 第三方功能库
    implementation libs.xxpermissions      // 权限管理库
    implementation libs.github.fastble     // 蓝牙低功耗(BLE)通信库
}